<?php

namespace app\modules\backend\controllers;

use Yii;
use yii\web\Response;
use app\common\models\MaterialStatusGroup;
use app\common\models\MaterialStatus;
use app\common\models\Supplier;
use app\common\models\Material;
use app\modules\api\services\rawMaterial\MaterialReturnAcceptService;
use app\modules\backend\controllers\BaseController;

/**
 * MaterialReturnController управляет подтверждением возврата материалов поставщикам
 */
class MaterialReturnController extends BaseController
{
    /**
     * Отображает список неподтвержденных возвратов материалов
     */
    public function actionIndex()
    {
        // Получаем неподтвержденные возвраты материалов
        $query = MaterialStatusGroup::find()
            ->select([
                'material_status_group.id',
                'material_status_group.add_user_id',
                'material_status_group.created_at',
                'material_status_group.supplier_id',
                'u.username as user_name',
                's.full_name as supplier_name',
                's.id as supplier_id'
            ])
            ->leftJoin('users u', 'u.id = material_status_group.add_user_id')
            ->leftJoin('supplier s', 's.id = material_status_group.supplier_id')
            ->where([
                'material_status_group.status' => MaterialStatusGroup::STATUS_RETURNED_TO_SUPPLIER,
                'material_status_group.deleted_at' => null,
                'material_status_group.accepted_at' => null,
                'material_status_group.accepted_user_id' => null
            ])
            ->orderBy(['material_status_group.created_at' => SORT_DESC]);

        $returnGroups = $query->asArray()->all();

        // Получаем материалы для каждой группы возврата
        foreach ($returnGroups as &$group) {
            $materials = MaterialStatus::find()
                ->select([
                    'material_status.id',
                    'material_status.material_id',
                    'm.name as material_name',
                    'material_status.quantity',
                    'm.unit_type',
                    'COALESCE(id.price, 0) as unit_price',
                    'COALESCE(id.price * material_status.quantity, 0) as total_price'
                ])
                ->leftJoin('material m', 'material_status.material_id = m.id')
                ->leftJoin(['id' => '(
                    WITH ranked_prices AS (
                        SELECT
                            material_id,
                            price,
                            id,
                            ROW_NUMBER() OVER (
                                PARTITION BY material_id
                                ORDER BY
                                    CASE WHEN price IS NOT NULL AND price > 0 THEN 0 ELSE 1 END,
                                    id DESC
                            ) as rn
                        FROM invoice_detail
                        WHERE deleted_at IS NULL
                    )
                    SELECT material_id, price
                    FROM ranked_prices
                    WHERE rn = 1
                )'], 'id.material_id = material_status.material_id')
                ->where([
                    'material_status.status_group_id' => $group['id'],
                    'material_status.deleted_at' => null
                ])
                ->asArray()
                ->all();

            // Добавляем название единицы измерения для каждого материала
            foreach ($materials as &$material) {
                $material['unit_type_name'] = Material::getUnitTypeName($material['unit_type'] ?? 1);

                // Обеспечиваем, что все поля не null
                $material['material_name'] = $material['material_name'] ?? '';
                $material['quantity'] = $material['quantity'] ?? 0;
                $material['unit_price'] = $material['unit_price'] ?? 0;
                $material['total_price'] = $material['total_price'] ?? 0;
            }

            $group['materials'] = $materials;
            $group['total_sum'] = array_sum(array_column($materials, 'total_price'));

            // Обеспечиваем, что все поля группы не null
            $group['supplier_name'] = $group['supplier_name'] ?? '';
            $group['user_name'] = $group['user_name'] ?? '';
            $group['suplier_id'] = $group['supplier_id'] ?? 0;
        }

        return $this->render('index', [
            'returnGroups' => $returnGroups
        ]);
    }

    /**
     * Подтверждает возврат материалов поставщику
     */
    public function actionAccept()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (!Yii::$app->request->isPost) {
            return [
                'status' => 'error',
                'message' => 'Метод не поддерживается'
            ];
        }

        $groupId = Yii::$app->request->post('group_id');
        $materialPrices = Yii::$app->request->post('material_prices', []);

        if (!$groupId) {
            return [
                'status' => 'error',
                'message' => 'ID группы возврата обязателен'
            ];
        }

        if (empty($materialPrices)) {
            return [
                'status' => 'error',
                'message' => 'Цены материалов обязательны'
            ];
        }

        // Проверяем права доступа
        if (!Yii::$app->user->can('raw_keeper')) {
            return [
                'status' => 'error',
                'message' => 'У вас нет прав для выполнения этого действия'
            ];
        }

        $service = new MaterialReturnAcceptService();
        $result = $service->acceptMaterialReturnWithPrices($groupId, $materialPrices);

        if ($result['success']) {
            return [
                'status' => 'success',
                'message' => $result['message']
            ];
        } else {
            return [
                'status' => 'error',
                'message' => $result['message']
            ];
        }
    }
}
