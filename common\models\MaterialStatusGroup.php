<?php

namespace app\common\models;

use Yii;
use app\modules\backend\models\Users;

/**
 * This is the model class for table "material_status_group".
 *
 * @property int $id
 * @property int $add_user_id
 * @property int $status
 * @property string $created_at
 * @property int|null $accepted_user_id
 * @property string|null $accepted_at
 * @property string|null $deleted_at
 * @property int|null $supplier_id
 *
 * @property Users $acceptedUser
 * @property Users $addUser
 * @property MaterialStatus[] $materialStatuses
 * @property Supplier $supplier
 */
class MaterialStatusGroup extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'material_status_group';
    }


    const STATUS_IN_STOCK = 1;      // На складе
    const STATUS_IN_PRODUCTION = 2; // В производстве
    const STATUS_DEFECT = 3; // На браке
    const STATUS_RETURNED_TO_SUPPLIER = 4; // Возврат поставщику
    const STATUS_RETURNED_FROM_PRODUCTION = 5; // Возврат из производства на склад
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_user_id', 'created_at'], 'required'],
            [['add_user_id', 'accepted_user_id', 'status', 'supplier_id'], 'default', 'value' => null],
            [['add_user_id', 'accepted_user_id', 'status', 'supplier_id'], 'integer'],
            [['created_at', 'accepted_at', 'deleted_at'], 'safe'],
            [['add_user_id'], 'exist', 'skipOnError' => true, 'targetClass' => Users::class, 'targetAttribute' => ['add_user_id' => 'id']],
            [['accepted_user_id'], 'exist', 'skipOnError' => true, 'targetClass' => Users::class, 'targetAttribute' => ['accepted_user_id' => 'id']],
            [['supplier_id'], 'exist', 'skipOnError' => true, 'targetClass' => Supplier::class, 'targetAttribute' => ['supplier_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_user_id' => 'Add User ID',
            'created_at' => 'Created At',
            'accepted_user_id' => 'Accepted User ID',
            'accepted_at' => 'Accepted At',
            'deleted_at' => 'Deleted At',
        ];
    }

    /**
     * Gets query for [[AcceptedUser]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAcceptedUser()
    {
        return $this->hasOne(Users::class, ['id' => 'accepted_user_id']);
    }

    /**
     * Gets query for [[AddUser]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAddUser()
    {
        return $this->hasOne(Users::class, ['id' => 'add_user_id']);
    }

    /**
     * Gets query for [[MaterialStatuses]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getMaterialStatuses()
    {
        return $this->hasMany(MaterialStatus::class, ['status_group_id' => 'id']);
    }

    /**
     * Gets query for [[Supplier]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getSupplier()
    {
        return $this->hasOne(Supplier::class, ['id' => 'supplier_id']);
    }
}
