<?php

namespace app\modules\api\services\manufacter;

use Yii;
use app\common\models\ActionLogger;
use app\common\models\MaterialDefect;
use app\common\models\MaterialStatusGroup;
use app\common\models\MaterialStatus;
use app\common\models\Tracking;
use app\modules\api\models\SendToMaterialDefectForm;
use yii\base\Component;

/**
 * Сервис для отправки материалов на брак в процессе производства
 */
class SendToMaterialDefectService extends Component
{
    /**
     * Отправка материалов на брак или возврат на склад
     *
     * @param array $postData Данные из POST-запроса
     * @return array Результат операции
     */
    public function sendToMaterialDefect($postData)
    {
        $model = new SendToMaterialDefectForm();
        $model->load($postData, '');

        if (!$model->validate()) {
            return [
                'success' => false,
                'message' => 'Ошибка валидации',
                'errors' => $model->getErrors()
            ];
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            if ($model->is_returned) {
                // Логика возврата материалов на склад (все материалы одной группой)
                $this->returnMaterialToStorage($model->materials, $model->description);
            } else {
                // Логика отправки материалов в брак (по отдельности)
                foreach ($model->materials as $material) {
                    $this->sendMaterialToDefect($material, $model->description);
                }
            }

            $transaction->commit();

            $message = $model->is_returned
                ? 'Материалы успешно возвращены на склад'
                : 'Материалы успешно отправлены на проверку брака';

            return [
                'success' => true,
                'message' => $message
            ];
        } catch (\Exception $e) {
            $transaction->rollBack();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Возврат материала на склад через создание MaterialStatusGroup
     * Требует подтверждения от RawMaterialController
     *
     * @param array $materials Массив материалов
     * @param string $description Описание причины возврата
     * @throws \Exception
     */
    private function returnMaterialToStorage($materials, $description = '')
    {
        // Создаем группу статусов материалов для возврата из производства
        $group = new MaterialStatusGroup();
        $group->add_user_id = Yii::$app->user->id;
        $group->status = MaterialStatusGroup::STATUS_RETURNED_FROM_PRODUCTION;
        $group->created_at = date('Y-m-d H:i:s');

        if (!$group->save()) {
            throw new \Exception('Ошибка сохранения группы материалов: ' . json_encode($group->getErrors()));
        }

        // Если materials - это один материал, приводим к массиву
        if (!isset($materials[0])) {
            $materials = [$materials];
        }

        foreach ($materials as $material) {
            // Создаем статус материала
            $materialStatus = new MaterialStatus();
            $materialStatus->material_id = $material['material_id'];
            $materialStatus->quantity = $material['quantity'];
            $materialStatus->status_group_id = $group->id;
            $materialStatus->created_at = date('Y-m-d H:i:s');

            if (!$materialStatus->save()) {
                throw new \Exception('Ошибка сохранения статуса материала: ' . json_encode($materialStatus->getErrors()));
            }

            // Примечание: Обновление склада произойдет при подтверждении возврата в RawMaterialController
        }

        // Создаем запись отслеживания для подтверждения
        $tracking = new Tracking();
        $tracking->progress_type = Tracking::TYPE_MATERIAL_RETURN;
        $tracking->process_id = $group->id;
        $tracking->created_at = date('Y-m-d H:i:s');
        $tracking->status = Tracking::STATUS_NOT_ACCEPTED;

        if (!$tracking->save()) {
            throw new \Exception('Ошибка сохранения tracking: ' . json_encode($tracking->getErrors()));
        }

        // Логируем действие
        ActionLogger::actionLog(
            'return_material_from_production',
            'material_status_group',
            $group->id,
            [
                'materials' => array_map(function($material) {
                    return [
                        'material_id' => $material['material_id'],
                        'quantity' => $material['quantity']
                    ];
                }, $materials),
                'description' => $description
            ]
        );
    }

    /**
     * Отправка материала в брак
     *
     * @param array $material Данные материала
     * @param string $description Описание брака
     * @throws \Exception
     */
    private function sendMaterialToDefect($material, $description = '')
    {
        $materialDefect = new MaterialDefect();
        $materialDefect->material_id = $material['material_id'];
        $materialDefect->quantity = $material['quantity'];
        $materialDefect->add_user_id = Yii::$app->user->id;
        $materialDefect->source = MaterialDefect::SOURCE_MANUFACTURER;
        $materialDefect->description = $description;
        $materialDefect->created_at = date('Y-m-d H:i:s');

        $materialDefect->accepted_user_id = Yii::$app->user->id;
        $materialDefect->accepted_at = date('Y-m-d H:i:s');

        if (!$materialDefect->save()) {
            throw new \Exception('Ошибка при сохранении брака: ' . json_encode($materialDefect->getErrors()));
        }

        // Создаем трекинг для брака
        $tracking = new Tracking();
        $tracking->progress_type = Tracking::TYPE_MATERIAL_DEFECT;
        $tracking->process_id = $materialDefect->id;
        $tracking->created_at = date('Y-m-d H:i:s');
        $tracking->status = Tracking::STATUS_NOT_ACCEPTED;
        $tracking->accepted_at = null;

        if (!$tracking->save()) {
            throw new \Exception('Ошибка при сохранении tracking: ' . json_encode($tracking->getErrors()));
        }

        // Логируем действие
        ActionLogger::actionLog(
            'send_to_defect',
            'material_defect',
            $materialDefect->id,
            [
                'material_id' => $materialDefect->material_id,
                'quantity' => $materialDefect->quantity,
                'description' => $materialDefect->description
            ]
        );
    }
}
